#!/usr/bin/env python3
"""
Test script for authentication and user context handling.
Tests the create_user_context function and its usage in API endpoints.

This test specifically catches issues where create_user_context is called
with incorrect parameters (like entire event or session_result dictionaries
instead of just the user_info dictionary).
"""

import json
import sys
import os
from typing import Dict, Any
import pytest

# Add the src directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)

from shared.api.auth_dependencies import create_user_context


class TestAuthUserContext:
    """Test cases for authentication and user context handling"""

    def test_create_user_context_with_valid_user_info(self):
        """Test create_user_context with valid user_info dictionary"""
        user_info = {
            "sub": "test-user-123",
            "email": "<EMAIL>",
            "username": "testuser",
            "cognito:groups": ["FundManagers"],
            "roles": ["FUND_MANAGER"]
        }
        
        result = create_user_context(user_info)
        
        assert result["user_id"] == "test-user-123"
        assert result["email"] == "<EMAIL>"
        assert result["username"] == "testuser"
        assert result["role"] == "FUND_MANAGER"
        assert result["groups"] == ["FundManagers"]

    def test_create_user_context_with_minimal_user_info(self):
        """Test create_user_context with minimal user_info"""
        user_info = {
            "sub": "test-user-456"
        }
        
        result = create_user_context(user_info)
        
        assert result["user_id"] == "test-user-456"
        assert result["role"] == "FUND_MANAGER"  # Default role

    def test_create_user_context_with_empty_dict(self):
        """Test create_user_context with empty dictionary"""
        user_info = {}
        
        result = create_user_context(user_info)
        
        assert result["user_id"] is None
        assert result["role"] == "FUND_MANAGER"  # Default role

    def test_create_user_context_with_event_dict_should_fail(self):
        """Test that create_user_context fails gracefully when passed entire event dict"""
        # This simulates the bug where entire event was passed instead of user_info
        event_dict = {
            "httpMethod": "POST",
            "path": "/funds/test-fund/snapshots/2025-01",
            "headers": {"Authorization": "Bearer token"},
            "body": '{"nav": 99}',
            "requestContext": {
                "authorizer": {
                    "sub": "test-user-123",
                    "email": "<EMAIL>"
                }
            }
        }
        
        # This should not crash but will return None for user_id since 'sub' is not at top level
        result = create_user_context(event_dict)
        
        # The function should handle this gracefully
        assert result["user_id"] is None  # No 'sub' at top level of event
        assert result["role"] == "FUND_MANAGER"  # Default role

    def test_create_user_context_with_session_result_dict_should_fail(self):
        """Test that create_user_context fails gracefully when passed entire session_result dict"""
        # This simulates the bug where entire session_result was passed instead of user_info
        session_result_dict = {
            "valid": True,
            "user_info": {
                "sub": "test-user-123",
                "email": "<EMAIL>"
            },
            "message": "Session valid"
        }
        
        # This should not crash but will return None for user_id since 'sub' is not at top level
        result = create_user_context(session_result_dict)
        
        # The function should handle this gracefully
        assert result["user_id"] is None  # No 'sub' at top level of session_result
        assert result["role"] == "FUND_MANAGER"  # Default role

    def test_simulate_fund_snapshot_creation_auth_flow(self):
        """Test the authentication flow that would be used in fund snapshot creation"""
        # Simulate the correct flow
        session_result = {
            "valid": True,
            "user_info": {
                "sub": "test-user-123",
                "email": "<EMAIL>",
                "username": "testuser",
                "cognito:groups": ["FundManagers"]
            },
            "message": "Session valid"
        }
        
        # This is the CORRECT way to call create_user_context
        user_context = create_user_context(session_result.get("user_info", {}))
        
        assert user_context["user_id"] == "test-user-123"
        assert user_context["email"] == "<EMAIL>"
        assert user_context["role"] == "FUND_MANAGER"
        
        # Simulate what would happen in the fund snapshot creation
        user_id = user_context.get("user_id")
        assert user_id is not None
        assert user_id == "test-user-123"

    def test_simulate_incorrect_auth_flow_that_caused_bug(self):
        """Test the incorrect authentication flow that caused the original bug"""
        # Simulate the incorrect flow that was causing the error
        session_result = {
            "valid": True,
            "user_info": {
                "sub": "test-user-123",
                "email": "<EMAIL>",
                "username": "testuser"
            },
            "message": "Session valid"
        }
        
        # This was the INCORRECT way that was causing the bug
        # create_user_context(session_result)  # Wrong - passing entire session_result
        # create_user_context(event)           # Wrong - passing entire event
        
        # Instead of crashing, the function should handle it gracefully
        user_context_wrong = create_user_context(session_result)  # This was the bug
        
        # The function handles this gracefully but returns None for user_id
        assert user_context_wrong["user_id"] is None  # No 'sub' at top level
        
        # This would cause the error in the fund snapshot creation:
        # user_id = user_context.get("user_id")  # This would be None
        # if not user_id: return error
        
        # The CORRECT way:
        user_context_correct = create_user_context(session_result.get("user_info", {}))
        assert user_context_correct["user_id"] == "test-user-123"


def test_auth_context_integration():
    """Integration test for authentication context handling"""
    # Test the complete flow that should happen in API endpoints
    
    # 1. Session validation result (simulated)
    session_result = {
        "valid": True,
        "user_info": {
            "sub": "test-user-789",
            "email": "<EMAIL>",
            "username": "integrationuser",
            "cognito:groups": ["Admins"],
            "roles": ["ADMIN"]
        }
    }
    
    # 2. Create user context correctly
    user_context = create_user_context(session_result.get("user_info", {}))
    
    # 3. Verify user context is valid for API operations
    assert user_context["user_id"] == "test-user-789"
    assert user_context["role"] == "ADMIN"
    assert user_context["email"] == "<EMAIL>"
    
    # 4. Simulate fund snapshot creation parameters
    fund_id = "test-fund-123"
    snapshot_data = {"nav": 99, "notes": "test"}
    user_id = user_context.get("user_id")
    
    # 5. Verify all required parameters are available
    assert fund_id is not None
    assert snapshot_data is not None
    assert user_id is not None
    assert user_id == "test-user-789"
    
    print("✅ Authentication context integration test passed")


if __name__ == "__main__":
    # Run the tests
    test_instance = TestAuthUserContext()
    
    print("🔐 Testing authentication and user context handling...")
    
    try:
        test_instance.test_create_user_context_with_valid_user_info()
        print("✅ Valid user_info test passed")
        
        test_instance.test_create_user_context_with_minimal_user_info()
        print("✅ Minimal user_info test passed")
        
        test_instance.test_create_user_context_with_empty_dict()
        print("✅ Empty dict test passed")
        
        test_instance.test_create_user_context_with_event_dict_should_fail()
        print("✅ Event dict handling test passed")
        
        test_instance.test_create_user_context_with_session_result_dict_should_fail()
        print("✅ Session result dict handling test passed")
        
        test_instance.test_simulate_fund_snapshot_creation_auth_flow()
        print("✅ Fund snapshot auth flow test passed")
        
        test_instance.test_simulate_incorrect_auth_flow_that_caused_bug()
        print("✅ Incorrect auth flow simulation test passed")
        
        test_auth_context_integration()
        
        print("\n🎉 All authentication and user context tests passed!")
        print("✅ The create_user_context function handles various input types gracefully")
        print("✅ The bug where incorrect parameters caused 'dict' object has no attribute 'user_id' is prevented")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
